<script setup lang="ts">
export interface InfoBadgesProps {
  /**
   * The title text to display
   */
  title?: string
  /**
   * The description text to display
   */
  text?: string
  /**
   * The main image source
   */
  image?: string
  /**
   * The small badge image source
   */
  badgeSmall?: string
  /**
   * The medium badge image source
   */
  badgeMedium?: string
  /**
   * The size of the main image
   */
  imageSize?: 'sm' | 'md' | 'lg' | 'xl'
  /**
   * The size of the small badge
   */
  badgeSmallSize?: 'xs' | 'sm' | 'md'
  /**
   * The size of the medium badge
   */
  badgeMediumSize?: 'sm' | 'md' | 'lg'
  /**
   * Whether to show badges
   */
  showBadges?: boolean
  /**
   * Custom CSS classes for the main image
   */
  imageClasses?: string
  /**
   * Alt text for images
   */
  imageAlt?: string
}

const props = withDefaults(defineProps<InfoBadgesProps>(), {
  title: 'Profile Title',
  text: 'Profile description text',
  image: '/img/avatars/10.svg',
  badgeSmall: '/img/avatars/5.svg',
  badgeMedium: '/img/avatars/8.svg',
  imageSize: 'lg',
  badgeSmallSize: 'sm',
  badgeMediumSize: 'md',
  showBadges: true,
  imageClasses: '',
  imageAlt: 'Profile image',
})

const imageSizeClasses = computed(() => {
  const sizes = {
    sm: 'size-32',
    md: 'size-40',
    lg: 'size-48',
    xl: 'size-56',
  }
  return sizes[props.imageSize] || sizes.lg
})

const badgeSmallSizeClasses = computed(() => {
  const sizes = {
    xs: 'size-10',
    sm: 'size-14',
    md: 'size-16',
  }
  return sizes[props.badgeSmallSize] || sizes.sm
})

const badgeMediumSizeClasses = computed(() => {
  const sizes = {
    sm: 'size-14',
    md: 'size-16',
    lg: 'size-20',
  }
  return sizes[props.badgeMediumSize] || sizes.md
})
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="relative mb-4">
      <img
        :class="['mx-auto rounded-full', imageSizeClasses, props.imageClasses]"
        :src="props.image"
        :alt="props.imageAlt"
      >
      <img
        v-if="props.showBadges && props.badgeSmall"
        :class="[
          'dark:border-muted-800 absolute start-2 top-2 rounded-full border-2 border-white',
          badgeSmallSizeClasses
        ]"
        :src="props.badgeSmall"
        :alt="`${props.imageAlt} badge`"
      >
      <img
        v-if="props.showBadges && props.badgeMedium"
        :class="[
          'dark:border-muted-800 absolute bottom-2 end-2 rounded-full border-2 border-white',
          badgeMediumSizeClasses
        ]"
        :src="props.badgeMedium"
        :alt="`${props.imageAlt} badge`"
      >
    </div>
    <div class="text-center mt-auto">
      <slot>
        <BaseHeading
          v-if="props.title"
          as="h3"
          size="md"
          weight="medium"
          lead="tight"
          class="text-muted-900 mb-1 dark:text-white"
        >
          <span>{{ props.title }}</span>
        </BaseHeading>
        <BaseParagraph v-if="props.text" size="xs">
          <span class="text-muted-600 dark:text-muted-400">{{ props.text }}</span>
        </BaseParagraph>
      </slot>
    </div>
  </div>
</template>
