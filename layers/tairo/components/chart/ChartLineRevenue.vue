<script setup lang="ts">
import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 250,
  
  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Revenue',
      data: [10835, 40214, 36257, 51411, 45697, 61221, 65295, 91512, 75648],
    },
  ],
  
  // X-axis categories - default matches original months
  categories: () => [
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
  ],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)'],
  
  // Chart title
  title: '',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
  
  // Y-axis formatter
  yAxisFormatter: (value: any) => formatPrice(value),
})

// Keep the original composable pattern but now use props
const lineRevenue = reactive(useLineRevenue())

function useLineRevenue() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'line',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
        fontFamily: 'var(--font-sans)',
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      grid: {
        row: {
          colors: ['transparent', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5,
        },
      },
      xaxis: {
        categories: props.categories,
      },
      tooltip: {
        y: {
          formatter: props.yAxisFormatter,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="lineRevenue" />
</template>
