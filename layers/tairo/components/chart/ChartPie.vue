<script setup lang="ts">
import type { DonutPieChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<DonutPieChartProps>(), {
  // Chart dimensions
  height: 335,
  
  // Data values - default matches original hardcoded data
  series: () => [44, 55, 13, 43, 22],
  
  // Labels for each slice
  labels: () => ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-primary-300)', 'var(--color-amber-400)', 'var(--color-indigo-400)', 'var(--color-teal-400)'],
  
  // Chart title
  title: '',
  
  // Legend settings
  legend: true,
  legendPosition: 'right',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const demoPie = reactive(useDemoPie())

function useDemoPie() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'pie',
    height: props.height,
    series: series.value,
    options: {
      dataLabels: {
        style: {
          fontSize: '12px',
          fontWeight: 500,
        },
      },
      colors: props.colors,
      labels: props.labels,
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 315,
              toolbar: {
                show: props.toolbar,
              },
            },
            legend: {
              position: 'top',
            },
          },
        },
      ],
      legend: {
        show: props.legend,
        position: props.legendPosition,
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoPie" />
</template>
