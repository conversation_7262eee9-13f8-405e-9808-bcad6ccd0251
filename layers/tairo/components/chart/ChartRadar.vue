<script setup lang="ts">
import type { RadarChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<RadarChartProps>(), {
  height: 350,
  
  // Default series data - matches original implementation
  series: () => [
    {
      name: 'Series 1',
      data: [80, 50, 30, 40, 100, 20],
    },
  ],
  
  // Categories
  categories: () => ['January', 'February', 'March', 'April', 'May', 'June'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-success-500)', 'var(--color-info-500)', 'var(--color-destructive-500)'],
  
  // Chart title
  title: '',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

const demoRadar = reactive(useDemoRadar())

function useDemoRadar() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      title: {
        text: props.title,
      },
      xaxis: {
        categories: props.categories,
      },
    },
  })
}
</script>

<template>
  <div class="relative">
    <BaseCard class="p-6">
      <!-- Title -->
      <div class="mb-6">
        <BaseHeading
          as="h3"
          size="md"
          weight="semibold"
          lead="tight"
          class="text-muted-800 dark:text-white"
        >
          <span>Radar Chart</span>
        </BaseHeading>
      </div>
      <LazyAddonApexcharts v-bind="demoRadar" />
    </BaseCard>
  </div>
</template>