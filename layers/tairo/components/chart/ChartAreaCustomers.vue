<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<MultiSeriesChartProps>(), {
  // Chart dimensions
  height: 280,
  
  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Returning',
      data: [31, 40, 28, 51, 42, 109, 100],
    },
    {
      name: 'Newcomers',
      data: [11, 32, 45, 32, 34, 52, 41],
    },
    {
      name: 'Abandonned',
      data: [78, 53, 36, 10, 14, 5, 2],
    },
  ],
  
  // X-axis categories - default matches original dates
  categories: () => [
    '2020-09-19T00:00:00.000Z',
    '2020-09-20T01:30:00.000Z',
    '2020-09-21T02:30:00.000Z',
    '2020-09-22T03:30:00.000Z',
    '2020-09-23T04:30:00.000Z',
    '2020-09-24T05:30:00.000Z',
    '2020-09-25T06:30:00.000Z',
  ],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-indigo-500)', 'var(--color-primary-400)'],
  
  // Chart title
  title: undefined,
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
  
  // Stacking option
  stacked: false,
})

// Keep the original composable pattern but now use props
const areaCustomers = reactive(useAreaCustomers())

function useAreaCustomers() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        toolbar: {
          show: props.toolbar,
        },
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: props.animations,
        },
        stacked: props.stacked,
      },
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
      legend: {
        show: false,
        position: 'top',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="areaCustomers" />
</template>
