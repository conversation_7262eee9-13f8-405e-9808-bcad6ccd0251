<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 180,
  
  // Default series data - matches original implementation
  series: () => [65],
  
  // Labels
  labels: () => ['Growth'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)'],
  
  // Chart title
  title: '',
  
  // Show value label
  showValue: true,
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

const radialGrowth = reactive(useRadialGrowth())

function useRadialGrowth() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
        fontFamily: 'var(--font-sans)',
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          hollow: {
            size: '75%',
          },
          dataLabels: {
            show: props.showValue,
            name: {
              show: true,
              fontSize: '0.7rem',
              fontWeight: 400,
              offsetY: -10,
            },
            value: {
              show: true,
              fontWeight: 600,
              fontSize: '16px',
              offsetY: -5,
            },
          },
        },
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialGrowth" />
</template>