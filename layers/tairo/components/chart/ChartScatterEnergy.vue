<script setup lang="ts">
import type { ScatterBubbleChartProps } from '~/types/chart-props'

// Helper function for generating scatter data
function generateDayWiseTimeSeries(
  baseval: number,
  count: number,
  yrange: { min: number, max: number },
) {
  let i = 0
  const series = []
  while (i < count) {
    const y
      = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min

    series.push([baseval, y])
    baseval += 86400000
    i++
  }
  return series
}

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<ScatterBubbleChartProps>(), {
  height: 280,
  
  // Default series data - matches original implementation
  series: () => [
    {
      name: 'Toni<PERSON>',
      data: generateDayWiseTimeSeries(
        new Date('Oct 11 2020 GMT').getTime(),
        20,
        {
          min: 10,
          max: 60,
        },
      ),
    },
    {
      name: '<PERSON><PERSON>',
      data: generateDayWiseTimeSeries(
        new Date('Oct 11 2020 GMT').getTime(),
        20,
        {
          min: 10,
          max: 60,
        },
      ),
    },
    {
      name: 'Vital',
      data: generateDayWiseTimeSeries(
        new Date('Oct 11 2020 GMT').getTime(),
        30,
        {
          min: 10,
          max: 60,
        },
      ),
    },
  ],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-primary-200)', 'var(--color-primary-400)'],
  
  // Chart title
  title: '',
  
  // Axis configuration
  yAxis: () => ({
    max: 70,
  }),
  
  // Animation and toolbar settings
  animations: true,
  toolbar: false,
})

const scatterEnergy = reactive(useScatterEnergy())

function useScatterEnergy() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'scatter',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        zoom: {
          type: 'xy',
        },
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
        // show: false,
      },
      grid: {
        show: false,
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: false,
          },
        },
      },
      xaxis: {
        // show: false,
        type: 'datetime',
        ...(props.xAxis || {}),
      },
      yaxis: {
        show: false,
        max: props.yAxis?.max || 70,
        ...(props.yAxis || {}),
      },
      legend: {
        show: false,
        position: 'top',
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="scatterEnergy" />
</template>