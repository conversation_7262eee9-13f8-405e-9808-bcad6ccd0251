<script setup lang="ts">
import type { SparkChartProps } from '~/types/chart-props'

// Extended props for this specific spark chart
interface SparkSalesChartProps extends SparkChartProps {
  title?: string
  subtitle?: string
  labels?: string[]
  enableAnimation?: boolean
  animationInterval?: number
}

// Define props with proper TypeScript types and defaults
const props = withDefaults(defineProps<SparkSalesChartProps>(), {
  // Chart dimensions
  width: undefined,
  height: 130,
  
  // Data - default matches original hardcoded data
  data: () => randomizeArray([
    472,
    454,
    547,
    385,
    562,
    247,
    652,
    318,
    379,
    391,
    622,
    515,
    355,
    415,
    358,
    271,
    932,
    534,
    615,
    278,
    546,
    435,
    192,
    465,
  ]),
  
  // Color
  color: 'var(--color-info-500)',
  
  // Chart type
  type: 'area',
  
  // Titles
  title: 'Consolidated',
  subtitle: '$17,865.29',
  
  // Labels for x-axis
  labels: () => [...Array.from({ length: 24 }).keys()].map(n => `2020-10-0${n + 1}`),
  
  // Animation settings
  enableAnimation: true,
  animationInterval: 2000,
})

// Keep the original composable pattern but now use props
const sparkSalesFour = reactive(useAreaSparkSalesFour())

function useAreaSparkSalesFour() {
  const series = ref([
    {
      name: props.title || 'Consolidated',
      data: [...props.data],
    },
  ])

  let timeout: any

  onMounted(() => {
    if (props.enableAnimation) {
      radomizeTimeout()
    }
  })
  
  onBeforeUnmount(() => {
    clearTimeout(timeout)
  })

  function radomizeTimeout() {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      series.value[0]!.data.push(randomNumber(200, 600))
      series.value[0]!.data.shift()
      radomizeTimeout()
    }, props.animationInterval)
  }

  return defineApexchartsProps({
    type: props.type as 'area',
    height: props.height,
    series,
    options: {
      chart: {
        id: 'sparkline1',
        group: 'sparklines',
        sparkline: {
          enabled: true,
        },
        zoom: {
          enabled: false,
        },
        animations: {
          enabled: false,
        },
      },
      colors: [props.color],
      stroke: {
        width: [2],
        curve: 'straight',
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
      labels: props.labels,
      yaxis: {
        min: 0,
        labels: {
          minWidth: 100,
        },
      },
      xaxis: {
        type: 'datetime',
      },
      title: {
        text: props.title,
        offsetX: 5,
        style: {
          fontFamily: 'var(--font-alt)',
          fontSize: '12px',
          fontWeight: '500',
          color: 'var(--color-chart-title)',
        },
      },
      subtitle: {
        text: props.subtitle,
        offsetX: 5,
        offsetY: 15,
        style: {
          fontFamily: 'var(--font-alt)',
          fontSize: '22px',
          fontWeight: '500',
          color: 'var(--color-chart-subtitle)',
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="sparkSalesFour" />
</template>
