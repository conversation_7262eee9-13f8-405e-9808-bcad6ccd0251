<script setup lang="ts">
import type { ScatterBubbleChartProps, ChartSeriesXY } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<ScatterBubbleChartProps>(), {
  // Chart dimensions
  height: 280,
  
  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Corporate',
      data: [
        {
          x: 'Team A',
          y: [1, 5],
        },
        {
          x: 'Team B',
          y: [4, 6],
        },
        {
          x: 'Team C',
          y: [5, 8],
        },
        {
          x: 'Team D',
          y: [3, 11],
        },
      ],
    },
    {
      name: 'Service',
      data: [
        {
          x: 'Team A',
          y: [2, 6],
        },
        {
          x: 'Team B',
          y: [1, 3],
        },
        {
          x: 'Team C',
          y: [7, 8],
        },
        {
          x: 'Team D',
          y: [5, 9],
        },
      ],
    },
  ] as ChartSeriesXY[],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-amber-400)'],
  
  // Chart title
  title: '',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const BarRange = reactive(useBarRange())

function useBarRange() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'rangeBar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      title: {
        text: props.title,
        align: 'left',
      },
      plotOptions: {
        bar: {
          horizontal: false,
        },
      },
      legend: {
        position: 'top',
        horizontalAlign: 'center',
      },
      dataLabels: {
        enabled: true,
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="BarRange" />
</template>
