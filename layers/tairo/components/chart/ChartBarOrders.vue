<script setup lang="ts">
import type { MultiSeriesChartProps } from '~/types/chart-props'

// Default data for backward compatibility
const defaultSeriesData = [
  {
    name: 'Orders',
    data: [
      { x: 'Jan', y: 322 },
      { x: 'Feb', y: 459 },
      { x: 'Mar', y: 212 },
      { x: 'Apr', y: 345 },
      { x: 'May', y: 111 },
      { x: 'Jun', y: 189 },
      { x: 'Jul', y: 498 },
      { x: 'Aug', y: 612 },
      { x: 'Sep', y: 451 },
      { x: 'Oct', y: 248 },
      { x: 'Nov', y: 306 },
      { x: 'Dec', y: 366 },
    ],
  },
  {
    name: 'Abandonned',
    data: [
      { x: 'Jan', y: 25 },
      { x: 'Feb', y: 49 },
      { x: 'Mar', y: 36 },
      { x: 'Apr', y: 84 },
      { x: 'May', y: 64 },
      { x: 'Jun', y: 131 },
      { x: 'Jul', y: 48 },
      { x: 'Aug', y: 144 },
      { x: 'Sep', y: 96 },
      { x: 'Oct', y: 11 },
      { x: 'Nov', y: 31 },
      { x: 'Dec', y: 8 },
    ],
  },
]

const props = withDefaults(defineProps<MultiSeriesChartProps & {
  delayedLoading?: boolean
}>(), {
  height: 210,
  series: () => defaultSeriesData,
  colors: () => ['var(--color-success-500)', 'var(--color-warning-500)'],
  title: '',
  animations: false,
  toolbar: false,
  delayedLoading: true,
})

const barOrders = reactive(useBarOrders())

function useBarOrders() {
  const series = ref<any[]>([])

  // delay the data loading
  let timeout: any
  let timeout2: any

  onMounted(() => {
    if (props.delayedLoading && props.series === defaultSeriesData) {
      // Use delayed loading for backward compatibility
      timeout = setTimeout(() => {
        series.value.push(props.series[0])
      }, 1500)

      timeout2 = setTimeout(() => {
        series.value.push(props.series[1])
      }, 2500)
    } else {
      // Use props directly if provided or delayedLoading is false
      series.value = [...props.series]
    }
  })
  onBeforeUnmount(() => {
    clearTimeout(timeout)
    clearTimeout(timeout2)
  })

  return defineApexchartsProps({
    type: 'bar',
    height: props.height,
    series,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
        zoom: {
          enabled: false,
        },
      },
      colors: props.colors,
      dataLabels: {
        enabled: false,
      },
      noData: {
        text: 'Loading...',
      },
      xaxis: {
        type: 'category',
        tickPlacement: 'on',
        labels: {
          rotate: -45,
          rotateAlways: true,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="barOrders" />
</template>
