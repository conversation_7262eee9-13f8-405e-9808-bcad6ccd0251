<script setup lang="ts">
import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 250,
  
  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Balance',
      data: [3143.16, 4298.49, 2876.54, 5183.76, 4232.87, 10876.56, 9543.12],
    },
  ],
  
  // X-axis categories - default matches original dates
  categories: () => [
    '2022-09-19T00:00:00.000Z',
    '2022-09-20T01:30:00.000Z',
    '2022-09-21T02:30:00.000Z',
    '2022-09-22T03:30:00.000Z',
    '2022-09-23T04:30:00.000Z',
    '2022-09-24T05:30:00.000Z',
    '2022-09-25T06:30:00.000Z',
  ],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)'],
  
  // Chart title
  title: '',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
  
  // Y-axis formatter
  yAxisFormatter: (value: any) => formatPrice(value),
})

// Keep the original composable pattern but now use props
const AreaBalance = reactive(useAreaBalance())

function useAreaBalance() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        animations: {
          enabled: props.animations,
        },
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'smooth',
      },
      colors: props.colors,
      legend: {
        show: false,
        position: 'top',
      },
      grid: {
        show: false,
        padding: {
          left: -10,
          right: 0,
          bottom: 10,
        },
      },
      xaxis: {
        type: 'datetime',
        categories: props.categories,
      },
      yaxis: {
        labels: {
          show: false,
          offsetX: -15,
        },
        axisBorder: {
          show: false,
        },
        axisTicks: {
          show: false,
        },
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
        y: {
          formatter: props.yAxisFormatter,
        },
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.75,
        },
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="AreaBalance" />
</template>
