<script setup lang="ts">
import type { DonutPieChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<DonutPieChartProps>(), {
  // Chart dimensions
  height: 290,
  
  // Data values - default matches original hardcoded data
  series: () => [44, 55, 41, 17, 15],
  
  // Labels for each slice
  labels: () => ['Team A', 'Team B', 'Team C', 'Team D', 'Team E'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-primary-300)', 'var(--color-amber-400)', 'var(--color-indigo-400)', 'var(--color-teal-400)'],
  
  // Chart title
  title: '',
  
  // Legend settings
  legend: true,
  legendPosition: 'right',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const demoDonut = reactive(useDemoDonut())

function useDemoDonut() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'donut',
    height: props.height,
    series: series.value,
    options: {
      title: {
        text: props.title,
      },
      labels: props.labels,
      colors: props.colors,
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              width: 280,
              toolbar: {
                show: props.toolbar,
              },
            },
            legend: {
              position: 'top',
            },
          },
        },
      ],
      legend: {
        show: props.legend,
        position: props.legendPosition,
        horizontalAlign: 'center',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="demoDonut" />
</template>
