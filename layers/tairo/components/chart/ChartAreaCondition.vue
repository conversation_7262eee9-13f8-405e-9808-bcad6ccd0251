<script setup lang="ts">
import type { SimpleSingleSeriesChartProps } from '~/types/chart-props'

// Define props with proper TypeScript types and defaults
// The default values MUST match the original hardcoded data to ensure backward compatibility
const props = withDefaults(defineProps<SimpleSingleSeriesChartProps>(), {
  // Chart dimensions
  height: 280,
  
  // Data series - default matches original hardcoded data
  series: () => [
    {
      name: 'Condition (pt)',
      data: [31, 40, 28, 51, 42, 109, 100],
    },
  ],
  
  // X-axis categories - default matches original months
  categories: () => ['Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)'],
  
  // Chart title
  title: '',
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

// Keep the original composable pattern but now use props
const areaCondition = reactive(useAreaCondition())

function useAreaCondition() {
  // Use computed to ensure reactivity when props change
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'area',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        offsetX: 20,
        zoom: {
          enabled: false,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      dataLabels: {
        enabled: false,
      },
      grid: {
        show: false,
      },
      stroke: {
        width: [2, 2, 2],
        curve: 'straight',
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          gradientToColors: ['var(--color-chart-gradient)'],
          shadeIntensity: 0,
          opacityFrom: 0.6,
          opacityTo: 0.1,
        },
      },
      colors: props.colors,
      labels: props.categories,
      yaxis: {
        opposite: true,
      },
      legend: {
        horizontalAlign: 'left',
      },
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="areaCondition" />
</template>
