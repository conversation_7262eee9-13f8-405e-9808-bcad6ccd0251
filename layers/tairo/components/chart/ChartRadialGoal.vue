<script setup lang="ts">
import type { RadialChartProps } from '~/types/chart-props'

// Define props with defaults matching original hardcoded values
const props = withDefaults(defineProps<RadialChartProps>(), {
  height: 220,
  
  // Default series data - matches original implementation
  series: () => [57, 86],
  
  // Labels
  labels: () => ['Efficiency', 'Productivity'],
  
  // Color scheme
  colors: () => ['var(--color-chart-base)', 'var(--color-indigo-500)'],
  
  // Chart title
  title: '',
  
  // Show value label
  showValue: true,
  
  // Animation and toolbar settings
  animations: false,
  toolbar: false,
})

const radialGoal = reactive(useRadialGoal())

function useRadialGoal() {
  // Use computed to ensure reactivity
  const series = computed(() => props.series)

  return defineApexchartsProps({
    type: 'radialBar',
    height: props.height,
    series: series.value,
    options: {
      chart: {
        offsetY: -10,
        fontFamily: 'var(--font-sans)',
        animations: {
          enabled: props.animations,
        },
        toolbar: {
          show: props.toolbar,
        },
      },
      colors: props.colors,
      plotOptions: {
        radialBar: {
          startAngle: -135,
          endAngle: 135,
          inverseOrder: true,
          dataLabels: {
            show: props.showValue,
            name: {
              show: true,
              fontSize: '14px',
              fontWeight: 500,
              offsetY: -10,
            },
            value: {
              show: true,
              fontWeight: 600,
              fontSize: '16px',
              offsetY: -5,
            },
            total: {
              show: true,
              fontSize: '14px',
              fontWeight: 500,
            },
          },
          hollow: {
            margin: 15,
            size: '75%',
          },
          track: {
            strokeWidth: '100%',
          },
        },
      },
      stroke: {
        lineCap: 'round',
      },
      labels: props.labels,
    },
  })
}
</script>

<template>
  <LazyAddonApexcharts v-bind="radialGoal" />
</template>