<script setup lang="ts">
export interface DashboardCtaCardProps {
  /**
   * The main title/heading text
   */
  title?: string
  /**
   * The description/subtitle text
   */
  description?: string
  /**
   * The link text for the action
   */
  linkText?: string
  /**
   * The URL for the action link
   */
  linkTo?: string
  /**
   * The icon name to display
   */
  icon?: string
  /**
   * The gradient color scheme
   */
  gradient?: 'primary' | 'info' | 'success' | 'warning' | 'danger'
  /**
   * Icon position
   */
  iconPosition?: 'bottom-right' | 'top-right' | 'bottom-left' | 'top-left'
  /**
   * Icon size
   */
  iconSize?: string
  /**
   * Whether to show the icon
   */
  showIcon?: boolean
}

const props = withDefaults(defineProps<DashboardCtaCardProps>(), {
  title: 'You\'re doing great!',
  description: 'Start using our team and project management tools',
  linkText: 'Learn More',
  linkTo: '#',
  icon: 'ph:crown-duotone',
  gradient: 'primary',
  iconPosition: 'bottom-right',
  iconSize: 'size-14',
  showIcon: true,
})

const gradientClasses = computed(() => {
  const gradients = {
    primary: 'from-primary-900 to-primary-800',
    info: 'from-info-900 to-info-800',
    success: 'from-success-900 to-success-800',
    warning: 'from-warning-900 to-warning-800',
    danger: 'from-danger-900 to-danger-800',
  }
  return gradients[props.gradient] || gradients.primary
})

const iconPositionClasses = computed(() => {
  const positions = {
    'bottom-right': 'absolute bottom-4 end-4 z-10',
    'top-right': 'absolute top-4 end-4 z-10',
    'bottom-left': 'absolute bottom-4 start-4 z-10',
    'top-left': 'absolute top-4 start-4 z-10',
  }
  return positions[props.iconPosition] || positions['bottom-right']
})

const iconColorClasses = computed(() => {
  const colors = {
    primary: 'text-primary-600/50',
    info: 'text-info-600/50',
    success: 'text-success-600/50',
    warning: 'text-warning-600/50',
    danger: 'text-danger-600/50',
  }
  return colors[props.gradient] || colors.primary
})
</script>

<template>
  <BaseCard
    variant="none"
    rounded="md"
    :class="[
      'relative flex h-full items-center justify-center bg-gradient-to-br p-6',
      gradientClasses
    ]"
  >
    <div class="relative z-20 flex flex-col gap-3 py-10 text-center">
      <BaseHeading
        as="h4"
        size="lg"
        weight="semibold"
        lead="tight"
        class="text-white"
      >
        <span>{{ props.title }}</span>
      </BaseHeading>
      <BaseParagraph size="md" class="mx-auto max-w-[280px]">
        <span class="text-white/80">
          {{ props.description }}
        </span>
      </BaseParagraph>
      <NuxtLink
        class="font-sans text-sm text-white underline-offset-4 hover:underline"
        :to="props.linkTo"
      >
        {{ props.linkText }}
      </NuxtLink>
    </div>
    <div
      v-if="props.showIcon"
      :class="[
        iconPositionClasses,
        'flex size-14 items-center justify-center'
      ]"
    >
      <Icon
        :name="props.icon"
        :class="[iconColorClasses, props.iconSize]"
      />
    </div>
  </BaseCard>
</template>
