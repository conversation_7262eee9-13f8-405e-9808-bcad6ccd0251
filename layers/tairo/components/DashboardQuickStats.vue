<script setup lang="ts">
export interface StatItem {
  icon: string
  value: string | number
  label: string
  color?: 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'amber' | 'green' | 'indigo'
}

export interface DashboardQuickStatsProps {
  /**
   * The title for the stats section
   */
  title?: string
  /**
   * The description text
   */
  description?: string
  /**
   * The link text for additional info
   */
  linkText?: string
  /**
   * The URL for the link
   */
  linkTo?: string
  /**
   * Array of stat items to display
   */
  stats?: StatItem[]
  /**
   * Grid columns for stats layout
   */
  gridCols?: 1 | 2 | 3 | 4
  /**
   * Whether to show the description section
   */
  showDescription?: boolean
}

const props = withDefaults(defineProps<DashboardQuickStatsProps>(), {
  title: 'Your Quick Stats',
  description: 'We\'ve selected some of your most important stats to keep you updated. You can always view more in the',
  linkText: 'reports section.',
  linkTo: '/dashboards',
  gridCols: 2,
  showDescription: true,
  stats: () => [
    {
      icon: 'ph:nut-duotone',
      value: '2,870',
      label: 'Sales this month',
      color: 'primary',
    },
    {
      icon: 'ph:handshake-duotone',
      value: '159',
      label: 'New users',
      color: 'amber',
    },
    {
      icon: 'ph:sketch-logo-duotone',
      value: '$429.18',
      label: 'Earned today',
      color: 'green',
    },
    {
      icon: 'ph:bank-duotone',
      value: '$6816.32',
      label: 'Total balance',
      color: 'indigo',
    },
  ],
})

const gridColsClass = computed(() => {
  const cols = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
  }
  return cols[props.gridCols] || cols[2]
})
</script>

<template>
  <BaseCard rounded="md" class="p-6 h-full flex flex-col">
    <div class="mb-6">
      <BaseHeading
        as="h3"
        size="md"
        weight="semibold"
        lead="tight"
        class="text-muted-800 dark:text-white"
      >
        <span>{{ props.title }}</span>
      </BaseHeading>
    </div>
    <div :class="['grid gap-4', gridColsClass, 'mt-auto']">
      <div v-if="props.showDescription" class="col-span-2">
        <BaseParagraph size="sm" class="text-muted-600 dark:text-muted-400">
          {{ props.description }}
          <BaseLink :to="props.linkTo" class="text-primary-600 dark:text-primary-400">
            {{ props.linkText }}
          </BaseLink>
        </BaseParagraph>
      </div>
      <!-- Stat Cards -->
      <DashboardStatCard
        v-for="(stat, index) in props.stats"
        :key="index"
        :icon="stat.icon"
        :value="stat.value"
        :label="stat.label"
        :color="stat.color"
      />
    </div>
  </BaseCard>
</template>
