@import 'tailwindcss';
@import '@shuriken-ui/nuxt';
@import '#layers/@cssninja/tairo/theme.css';

@theme {
  /* Custom screens combining breakpoints with orientation */
  --screen-lg-landscape: only screen and (min-width: 64rem) and (orientation: landscape);
  --screen-lg-portrait: only screen and (min-width: 64rem) and (orientation: portrait);
  --screen-2xl-landscape: only screen and (min-width: 96rem) and (orientation: landscape);
  --screen-2xl-portrait: only screen and (min-width: 96rem) and (orientation: portrait);
}

@layer base {
  .dark {
    --color-chart-gradient: var(--color-muted-950);
    --color-chart-title: var(--color-muted-400);
    --color-chart-subtitle: var(--color-white);
  }
}
