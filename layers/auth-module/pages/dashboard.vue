<script setup lang="ts">
definePageMeta({
  title: 'Activity',
  preview: {
    title: 'Personal dashboard v1',
    description: 'For personal usage and reports',
    categories: ['dashboards'],
    src: '/img/screens/dashboards-personal-1.png',
    srcDark: '/img/screens/dashboards-personal-1-dark.png',
    order: 1,
  },
})
</script>

<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <!-- Header -->
    <!-- Header -->
    <DashboardWelcomeHeader
      avatar-src="/img/avatars/10.svg"
      user-name="<PERSON>"
      subtitle="Happy to see you again on your dashboard."
      primary-button-text="Manage Store"
      secondary-button-text="View Reports"
      primary-button-to="#"
      secondary-button-to="#"
    />
    <!-- Grid -->
    <div class="grid grid-cols-12 gap-4">
      <!-- Row 1: Area Chart + Quick Stats -->
      <!-- Area Chart card -->
      <div class="col-span-12 lg:col-span-7">
        <BaseCard class="p-6">
          <!-- Title -->
          <div class="mb-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-800 dark:text-white"
            >
              <span>New / returning customers</span>
            </BaseHeading>
          </div>
          <ChartAreaCustomers />
        </BaseCard>
      </div>
      <!-- Quick stats -->
      <div class="col-span-12 lg:col-span-5">
        <DashboardQuickStats
          title="Your Quick Stats"
          description="We've selected some of your most important stats to keep you updated. You can always view more in the"
          link-text="reports section."
          link-to="/dashboards"
          :stats="[
            {
              icon: 'ph:nut-duotone',
              value: '2,870',
              label: 'Sales this month',
              color: 'primary',
            },
            {
              icon: 'ph:handshake-duotone',
              value: '159',
              label: 'New users',
              color: 'amber',
            },
            {
              icon: 'ph:sketch-logo-duotone',
              value: '$429.18',
              label: 'Earned today',
              color: 'green',
            },
            {
              icon: 'ph:bank-duotone',
              value: '$6816.32',
              label: 'Total balance',
              color: 'indigo',
            },
          ]"
        />
      </div>

      <!-- Row 2: Achievements + CTA -->
      <!-- Achievements card -->
      <div class="col-span-12 md:col-span-6">
        <BaseCard rounded="md" class="p-6 h-full">
          <InfoBadges
            class="h-full"
            image="/img/illustrations/widgets/1.svg"
            badge-small="/img/illustrations/widgets/3.svg"
            badge-medium="/img/illustrations/widgets/2.svg"
          >
            <div class="text-start">
              <BaseHeading
                as="h3"
                size="md"
                weight="medium"
                lead="tight"
                class="text-muted-800 mb-1 dark:text-white"
              >
                <span>You've unlocked 2 new Achievements</span>
              </BaseHeading>
              <BaseParagraph size="sm">
                <span class="text-muted-600 dark:text-muted-400">Congrats, your efforts have been rewarded. Keep up like this!</span>
              </BaseParagraph>
              <div class="mt-4">
                <BaseButton rounded="md" class="w-full">
                  <span>View Achievements</span>
                </BaseButton>
              </div>
            </div>
          </InfoBadges>
        </BaseCard>
      </div>
      <!-- CTA card -->
      <div class="col-span-12 md:col-span-6">
        <DashboardCtaCard
          title="You're doing great!"
          description="Start using our team and project management tools"
          link-text="Learn More"
          link-to="#"
          icon="ph:crown-duotone"
          gradient="primary"
        />
      </div>

      <!-- Row 3: Team Efficiency + Bar Chart -->
      <!-- Radial Bar card -->
      <div class="col-span-12 lg:col-span-5">
        <BaseCard rounded="md" class="relative flex flex-col h-full p-6">
          <div class="mb-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-800 dark:text-white"
            >
              <span>Team Efficiency</span>
            </BaseHeading>
          </div>
          <div
            class="absolute inset-x-0 top-24 flex items-center justify-center gap-4"
          >
            <BaseAvatar src="/img/avatars/4.svg" />
            <BaseAvatar
              text="H"
              class="bg-yellow-400/10 dark:bg-yellow-400/20 text-yellow-600"
            />
            <BaseAvatar src="/img/avatars/3.svg" />
          </div>
          <div class="mt-auto">
            <ChartRadialGaugeAlt />
          </div>
        </BaseCard>
      </div>
      <!-- Bar chart card -->
      <div class="col-span-12 lg:col-span-7">
        <BaseCard rounded="md" class="flex flex-col h-full p-6">
          <div class="mb-6">
            <BaseHeading
              as="h3"
              size="md"
              weight="semibold"
              lead="tight"
              class="text-muted-800 dark:text-white"
            >
              <span>Global expense report</span>
            </BaseHeading>
          </div>
          <div class="mt-auto">
            <ChartBarProfit />
          </div>
        </BaseCard>
      </div>

      <!-- Row 4: Transactions (full width) -->
      <div class="col-span-12">
        <!-- Transactions widget -->
        <WidgetTransactionSummary />
      </div>
    </div>
  </div>
</template>
