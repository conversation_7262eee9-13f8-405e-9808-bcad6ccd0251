<template>
  <div class="px-4 md:px-6 lg:px-8 pb-20">
    <div class="mx-auto w-full max-w-7xl">
      <h1 class="text-2xl font-bold mb-8">Grid System Test</h1>
      
      <!-- Test 1: Basic 12-column grid -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 1: Basic 12-column grid</h2>
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-6 bg-blue-100 p-4 rounded">6 columns</div>
          <div class="col-span-6 bg-green-100 p-4 rounded">6 columns</div>
        </div>
      </div>

      <!-- Test 2: 8/4 split like profile page -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 2: 8/4 split (like profile page)</h2>
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-12 lg:col-span-8 bg-blue-100 p-4 rounded">
            Main content (8 columns on lg+)
          </div>
          <div class="col-span-12 lg:col-span-4 bg-green-100 p-4 rounded">
            Sidebar (4 columns on lg+)
          </div>
        </div>
      </div>

      <!-- Test 3: Responsive grid -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 3: Responsive grid</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="bg-red-100 p-4 rounded">Item 1</div>
          <div class="bg-yellow-100 p-4 rounded">Item 2</div>
          <div class="bg-purple-100 p-4 rounded">Item 3</div>
        </div>
      </div>

      <!-- Test 4: Nested grids -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 4: Nested grids</h2>
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-12 lg:col-span-8 bg-blue-50 p-4 rounded">
            <div class="grid gap-x-4 gap-y-8 sm:grid-cols-2">
              <div class="bg-blue-200 p-4 rounded">Nested item 1</div>
              <div class="bg-blue-200 p-4 rounded">Nested item 2</div>
              <div class="bg-blue-200 p-4 rounded">Nested item 3</div>
              <div class="bg-blue-200 p-4 rounded">Nested item 4</div>
            </div>
          </div>
          <div class="col-span-12 lg:col-span-4 bg-green-50 p-4 rounded">
            <div class="space-y-4">
              <div class="bg-green-200 p-4 rounded">Sidebar item 1</div>
              <div class="bg-green-200 p-4 rounded">Sidebar item 2</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Test 5: Check if Tailwind classes are working -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 5: Basic Tailwind classes</h2>
        <div class="flex flex-col space-y-4">
          <div class="bg-primary-500 text-white p-4 rounded">Primary color</div>
          <div class="bg-muted-200 dark:bg-muted-700 p-4 rounded">Muted background</div>
          <div class="border border-muted-200 dark:border-muted-800 p-4 rounded">Border</div>
        </div>
      </div>

      <!-- Test 6: Custom orientation classes (like dashboard) -->
      <div class="mb-8">
        <h2 class="text-lg font-semibold mb-4">Test 6: Custom orientation classes</h2>
        <div class="grid grid-cols-12 gap-4">
          <div class="col-span-12 lg-portrait-col-span-6 lg-landscape-col-span-6 xl-landscape-col-span-3 bg-blue-100 p-4 rounded">
            <div class="text-sm">
              <strong>Orientation responsive:</strong><br>
              - Mobile: 12 cols<br>
              - lg portrait: 6 cols<br>
              - lg landscape: 6 cols<br>
              - xl landscape: 3 cols
            </div>
          </div>
          <div class="col-span-12 lg-landscape-col-span-6 xl-landscape-col-span-4 bg-green-100 p-4 rounded">
            <div class="text-sm">
              <strong>Landscape specific:</strong><br>
              - Mobile: 12 cols<br>
              - lg landscape: 6 cols<br>
              - xl landscape: 4 cols
            </div>
          </div>
          <div class="col-span-12 lg-landscape-col-span-6 xl-landscape-col-span-5 bg-yellow-100 p-4 rounded">
            <div class="text-sm">
              <strong>Another landscape:</strong><br>
              - Mobile: 12 cols<br>
              - lg landscape: 6 cols<br>
              - xl landscape: 5 cols
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  title: 'Grid Test',
})
</script>
