@import 'tailwindcss' theme(static);
@import '@shuriken-ui/nuxt';
@import '../../../layers/tairo/theme.css';
@plugin "@tailwindcss/typography";

@theme {
  --font-sans: 'Inter', sans-serif;
  --default-mono-font-family: 'Fira Code';
  --font-mono:
    var(--default-mono-font-family), ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  --font-alt: '<PERSON>a', sans-serif;
  --font-heading: var(--font-sans);

  --animate-spin-slow: spin 3s linear infinite;
  --animate-spin-fast: spin 0.65s linear infinite;

  --color-chart-base: var(--color-primary-600);
  --color-chart-gradient: var(--color-white);
  --color-chart-title: var(--color-muted-600);
  --color-chart-subtitle: var(--color-muted-900);

  --color-primary-50: var(--color-violet-50);
  --color-primary-100: var(--color-violet-100);
  --color-primary-200: var(--color-violet-200);
  --color-primary-300: var(--color-violet-300);
  --color-primary-400: var(--color-violet-400);
  --color-primary-500: var(--color-violet-500);
  --color-primary-600: var(--color-violet-600);
  --color-primary-700: var(--color-violet-700);
  --color-primary-800: var(--color-violet-800);
  --color-primary-900: var(--color-violet-900);
  --color-primary-950: var(--color-violet-950);
}

@layer base {
  .dark {
    --color-chart-gradient: var(--color-muted-950);
    --color-chart-title: var(--color-muted-400);
    --color-chart-subtitle: var(--color-white);
  }
}

/* Auth module specific styles */
@layer utilities {
  /* Custom orientation-based responsive utilities */
  @media (min-width: 64rem) and (orientation: landscape) {
    .lg-landscape-col-span-3 { grid-column: span 3 / span 3; }
    .lg-landscape-col-span-4 { grid-column: span 4 / span 4; }
    .lg-landscape-col-span-5 { grid-column: span 5 / span 5; }
    .lg-landscape-col-span-6 { grid-column: span 6 / span 6; }
    .lg-landscape-col-span-7 { grid-column: span 7 / span 7; }
    .lg-landscape-col-span-8 { grid-column: span 8 / span 8; }
    .lg-landscape-max-w-full { max-width: 100%; }
  }

  @media (min-width: 64rem) and (orientation: portrait) {
    .lg-portrait-col-span-3 { grid-column: span 3 / span 3; }
    .lg-portrait-col-span-4 { grid-column: span 4 / span 4; }
    .lg-portrait-col-span-5 { grid-column: span 5 / span 5; }
    .lg-portrait-col-span-6 { grid-column: span 6 / span 6; }
    .lg-portrait-col-span-7 { grid-column: span 7 / span 7; }
    .lg-portrait-col-span-8 { grid-column: span 8 / span 8; }
  }

  @media (min-width: 96rem) and (orientation: landscape) {
    .xl-landscape-col-span-3 { grid-column: span 3 / span 3; }
    .xl-landscape-col-span-4 { grid-column: span 4 / span 4; }
    .xl-landscape-col-span-5 { grid-column: span 5 / span 5; }
    .xl-landscape-col-span-6 { grid-column: span 6 / span 6; }
    .xl-landscape-col-span-7 { grid-column: span 7 / span 7; }
    .xl-landscape-col-span-8 { grid-column: span 8 / span 8; }
  }

  @media (min-width: 96rem) and (orientation: portrait) {
    .xl-portrait-col-span-3 { grid-column: span 3 / span 3; }
    .xl-portrait-col-span-4 { grid-column: span 4 / span 4; }
    .xl-portrait-col-span-5 { grid-column: span 5 / span 5; }
    .xl-portrait-col-span-6 { grid-column: span 6 / span 6; }
    .xl-portrait-col-span-7 { grid-column: span 7 / span 7; }
    .xl-portrait-col-span-8 { grid-column: span 8 / span 8; }
  }
}
